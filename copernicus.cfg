\newif\ifprp             \DeclareOption{prp}     {\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue                  \@bartrue \prptrue}
\newif\ifgtes            \DeclareOption{gtes}    {\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue                  \@bartrue \gtestrue}
\newif\ifdwes            \DeclareOption{dwes}    {\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@twostagejnltrue \@barfalse\dwestrue}
                         \DeclareOption{dwesd}   {\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@stage@finalfalse          \dwestrue}
\newif\ifcopyediting     \DeclareOption{copyediting}{\copyeditingtrue\@noreftrue}
\newif\ifsmsps           \DeclareOption{smsps}   {                  \smspstrue}
\newif\ifar  \DeclareOption{ar}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@twostagejnltrue\artrue}
            \DeclareOption{ard}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@stage@finalfalse\artrue}
\newif\ifsp  \DeclareOption{sp}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@twostagejnltrue\sptrue}
            \DeclareOption{spd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@stage@finalfalse\sptrue}
\newif\ifegusphere  \DeclareOption{egusphere}{\@twostagejnltrue\eguspheretrue}
            \DeclareOption{egusphered}{\@stage@finalfalse\eguspheretrue}
\newif\ifsand  \DeclareOption{sand}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@twostagejnltrue\sandtrue}
            \DeclareOption{sandd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@stage@finalfalse\sandtrue}
\newif\ifpolf  \DeclareOption{polf}{\@sansseriffacetrue\@sansserifheadertrue\@bartrue\polftrue}
\newif\ifjbji  \DeclareOption{jbji}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\jbjitrue}
\newif\ifmr  \DeclareOption{mr}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@twostagejnltrue\mrtrue}
            \DeclareOption{mrd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@stage@finalfalse\mrtrue}
\newif\ifejm  \DeclareOption{ejm}{\@abstractcenteredtrue\@bartrue\ejmtrue}
\newif\ifwcd  \DeclareOption{wcd}{\@twostagejnltrue\wcdtrue}
            \DeclareOption{wcdd}{\@stage@finalfalse\wcdtrue}
\newif\ifgchron  \DeclareOption{gchron}{\@sansseriffacetrue\@sansserifheadertrue\@twostagejnltrue\gchrontrue}
            \DeclareOption{gchrond}{\@sansseriffacetrue\@sansserifheadertrue\@stage@finalfalse\gchrontrue}
\newif\ifdeuquasp  \DeclareOption{deuquasp}{\@sansseriffacetrue\@sansserifheadertrue\@abstractindentedtrue\@bartrue\@seclinetrue\deuquasptrue}
\newif\ifgc  \DeclareOption{gc}{\@sansseriffacetrue\@sansserifheadertrue\@bartrue\@twostagejnltrue\gctrue}
            \DeclareOption{gcd}{\@sansseriffacetrue\@sansserifheadertrue\@bartrue\@stage@finalfalse\gctrue}
\newif\ifegqsj  \DeclareOption{egqsj}{\@sansseriffacetrue\@sansserifheadertrue\@abstractindentedtrue\@bartrue\@seclinetrue\egqsjtrue}
\newif\ifjm  \DeclareOption{jm}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\jmtrue}
\newif\ifwes  \DeclareOption{wes}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@twostagejnltrue\westrue}
            \DeclareOption{wesd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@stage@finalfalse\westrue}
\newif\ifaab  \DeclareOption{aab}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\aabtrue}
\newif\ifpiahs  \DeclareOption{piahs}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\piahstrue}
\newif\ifascmo  \DeclareOption{ascmo}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@corrigendumtrue\@editorialnotetwoctrue\ascmotrue}
\newif\ifpb  \DeclareOption{pb}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\pbtrue}
\newif\ifsoil  \DeclareOption{soil}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@twostagejnltrue\soiltrue}
            \DeclareOption{soild}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@stage@finalfalse\soiltrue}
\newif\ifangeocom  \DeclareOption{angeocom}{\@bartrue\@twostagejnltrue\angeocomtrue}
            \DeclareOption{angeocomd}{\@bartrue\@stage@finalfalse\angeocomtrue}
\newif\ifwe  \DeclareOption{we}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\wetrue}
\newif\ifsdpd  \DeclareOption{sdpd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\sdpdtrue}
\newif\ifsdwr  \DeclareOption{sdwr}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\sdwrtrue}
\newif\ifsdtd  \DeclareOption{sdtd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\sdtdtrue}
\newif\ifsdpr  \DeclareOption{sdpr}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\sdprtrue}
\newif\ifsdsr  \DeclareOption{sdsr}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\sdsrtrue}
\newif\ifasr  \DeclareOption{asr}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\asrtrue}
\newif\ifms  \DeclareOption{ms}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\mstrue}
\newif\ifjsss  \DeclareOption{jsss}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\jssstrue}
\newif\ifhgss  \DeclareOption{hgss}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@twostagejnltrue\hgsstrue}
            \DeclareOption{hgssd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@stage@finalfalse\hgsstrue}
\newif\ifgh  \DeclareOption{gh}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\ghtrue}
\newif\ifesurf  \DeclareOption{esurf}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@twostagejnltrue\esurftrue}
            \DeclareOption{esurfd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@stage@finalfalse\esurftrue}
\newif\ifessd  \DeclareOption{essd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@twostagejnltrue\essdtrue}
            \DeclareOption{essdd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@stage@finalfalse\essdtrue}
\newif\iftc  \DeclareOption{tc}{\@twostagejnltrue\tctrue}
            \DeclareOption{tcd}{\@stage@finalfalse\tctrue}
\newif\ifse  \DeclareOption{se}{\@bartrue\@twostagejnltrue\setrue}
            \DeclareOption{sed}{\@bartrue\@stage@finalfalse\setrue}
\newif\ifos  \DeclareOption{os}{\@twostagejnltrue\ostrue}
            \DeclareOption{osd}{\@stage@finalfalse\ostrue}
\newif\ifnpg  \DeclareOption{npg}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@editorialnotedtrue\@twostagejnltrue\npgtrue}
            \DeclareOption{npgd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@stage@finalfalse\npgtrue}
\newif\ifnhess  \DeclareOption{nhess}{\@editorialnotedtrue\@twostagejnltrue\nhesstrue}
            \DeclareOption{nhessd}{\@stage@finalfalse\nhesstrue}
\newif\ifhess  \DeclareOption{hess}{\@twostagejnltrue\hesstrue}
            \DeclareOption{hessd}{\@stage@finalfalse\hesstrue}
\newif\ifgmd  \DeclareOption{gmd}{\@firstbartrue\@twostagejnltrue\gmdtrue}
            \DeclareOption{gmdd}{\@firstbartrue\@stage@finalfalse\gmdtrue}
\newif\ifgi  \DeclareOption{gi}{\@corrigendumtrue\@editorialnotetwoctrue\@editorialnotedtrue\@twostagejnltrue\gitrue}
            \DeclareOption{gid}{\@stage@finalfalse\gitrue}
\newif\ifesd  \DeclareOption{esd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@twostagejnltrue\esdtrue}
            \DeclareOption{esdd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@bartrue\@stage@finalfalse\esdtrue}
\newif\ifcp  \DeclareOption{cp}{\@sansseriffacetrue\@sansserifheadertrue\@twostagejnltrue\cptrue}
            \DeclareOption{cpd}{\@sansseriffacetrue\@sansserifheadertrue\@stage@finalfalse\cptrue}
\newif\ifastra  \DeclareOption{astra}{\astratrue}
\newif\ifars  \DeclareOption{ars}{\arstrue}
\newif\ifangeo  \DeclareOption{angeo}{\@twostagejnltrue\angeotrue}
            \DeclareOption{angeod}{\@stage@finalfalse\angeotrue}
\newif\ifamt  \DeclareOption{amt}{\@twostagejnltrue\amttrue}
            \DeclareOption{amtd}{\@stage@finalfalse\amttrue}
\newif\ifadgeo  \DeclareOption{adgeo}{\adgeotrue}
\newif\iffr  \DeclareOption{fr}{\frtrue}
\newif\ifbg  \DeclareOption{bg}{\@firstbartrue\@twostagejnltrue\bgtrue}
            \DeclareOption{bgd}{\@firstbartrue\@stage@finalfalse\bgtrue}
\newif\ifacp  \DeclareOption{acp}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@twostagejnltrue\acptrue}
            \DeclareOption{acpd}{\@sansseriffacetrue\@sansserifheadertrue\@abstractcenteredtrue\@firstbartrue\@stage@finalfalse\acptrue}



\def\@addjournalconfig{%


\ifcopyediting% Definition of layout used for copy-editing track changes document
  \def\@journalurl{}
  \def\@sentence{\textcolor[rgb]{1,0.58,0.25}{%
    Copy-Editing track changes document -- Do not use for proof-reading}}
  \let\@journalnameabbreviation\@sentence
  \if@sansserifface
    \definecolor{textcol}{rgb}{0,0,0}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{gray}{0.8}
    \definecolor{rulecol}{gray}{0.8}
  \fi
\fi

\ifacp%classical
  \def\@journalname{Atmospheric Chemistry and Physics}
  \def\@journalnameabbreviation{Atmos. Chem. Phys.}
  \def\@journalnameshort{ACP}
  \def\@journalnameshortlower{acp}
  \def\@journalstartyear{2001}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.atmos-chem-phys.net}
    \def\@journallogo{\includegraphics{ACP_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.0,0.447,0.737}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.0,0.447,0.737}
  \else
    \def\@journalurl{www.atmos-chem-phys-discuss.net}
    \def\@journallogo{\includegraphics{ACPD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Atmospheric Chemistry\\ and Physics (ACP). Please refer to the corresponding final paper in ACP if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.0,0.58,0.91}
      \definecolor{buttonbackground}{rgb}{0.0,0.58,0.91}
      \definecolor{paneltext}{rgb}{0.0,0.58,0.91}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifbg%classical
  \def\@journalname{Biogeosciences}
  \def\@journalnameabbreviation{Biogeosciences}
  \def\@journalnameshort{BG}
  \def\@journalnameshortlower{bg}
  \def\@journalstartyear{2004}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.biogeosciences.net}
    \def\@journallogo{\includegraphics{BG_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.0,0.0,0.0}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.612,0.698,0.145}
  \else
    \def\@journalurl{www.biogeosciences-discuss.net}
    \def\@journallogo{\includegraphics{BGD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Biogeosciences (BG).\\ Please refer to the corresponding final paper in BG if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.612,0.698,0.145}
      \definecolor{buttonbackground}{rgb}{0.612,0.698,0.145}
      \definecolor{paneltext}{rgb}{0.612,0.698,0.145}
      \definecolor{buttontext}{rgb}{0.612,0.0,0.055}
    \fi
  \fi
\fi
\iffr%classical
  \def\@journalname{Fossil Record}
  \def\@journalnameabbreviation{Foss. Rec.}
  \def\@journalnameshort{FR}
  \def\@journalnameshortlower{fr}
  \def\@journalstartyear{1998}
  \def\@sentence{Published by Copernicus Publications on behalf of the Museum f{\"u}r Naturkunde Berlin.}
  \def\@journalurl{www.foss-rec.net}
  \def\@journallogo{\includegraphics{FR_Logo.pdf}}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifadgeo%classical
  \def\@journalname{Advances in Geosciences}
  \def\@journalnameabbreviation{Adv. Geosci.}
  \def\@journalnameshort{ADGEO}
  \def\@journalnameshortlower{adgeo}
  \def\@journalstartyear{2013}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \def\@journalurl{www.adv-geosci.net}
  \def\@journallogo{\includegraphics{ADGEO_Logo.pdf}}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifamt%classical
  \def\@journalname{Atmospheric Measurement Techniques}
  \def\@journalnameabbreviation{Atmos. Meas. Tech.}
  \def\@journalnameshort{AMT}
  \def\@journalnameshortlower{amt}
  \def\@journalstartyear{2008}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.atmos-meas-tech.net}
    \def\@journallogo{\includegraphics{AMT_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.atmos-meas-tech-discuss.net}
    \def\@journallogo{\includegraphics{AMTD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Atmospheric Measurement\\ Techniques (AMT). Please refer to the corresponding final paper in AMT if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.0,0.259,0.475}
      \definecolor{buttonbackground}{rgb}{0.0,0.259,0.475}
      \definecolor{paneltext}{rgb}{0.0,0.259,0.475}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifangeo%classical
  \def\@journalname{Annales Geophysicae}
  \def\@journalnameabbreviation{Ann. Geophys.}
  \def\@journalnameshort{ANGEO}
  \def\@journalnameshortlower{angeo}
  \def\@journalstartyear{1983}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.ann-geophys.net}
    \def\@journallogo{\includegraphics{ANGEO_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{}
    \def\@journallogo{}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifars%classical
  \def\@journalname{Advances in Radio Sciences}
  \def\@journalnameabbreviation{Adv. Radio Sci.}
  \def\@journalnameshort{ARS}
  \def\@journalnameshortlower{ars}
  \def\@journalstartyear{2003}
  \def\@sentence{Published by Copernicus Publications on behalf of the URSI Landesausschuss in der Bundesrepublik Deutschland e.V.}
  \def\@journalurl{www.adv-radio-sci.net}
  \def\@journallogo{\includegraphics{ARS_Logo.pdf}}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifastra%classical
  \def\@journalname{Astrophysics and Space Sciences Transactions}
  \def\@journalnameabbreviation{Astrophys. Space Sci. Trans.}
  \def\@journalnameshort{ASTRA}
  \def\@journalnameshortlower{astra}
  \def\@journalstartyear{2004}
  \def\@sentence{Published by Copernicus Publications on behalf of the Arbeitsgemeinschaft Extraterrestrische Forschung e.V.}
  \def\@journalurl{www.astrophys-space-sci-trans.net}
  \def\@journallogo{}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifcp%classical
  \def\@journalname{Climate of the Past}
  \def\@journalnameabbreviation{Clim. Past}
  \def\@journalnameshort{CP}
  \def\@journalnameshortlower{cp}
  \def\@journalstartyear{2005}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.clim-past.net}
    \def\@journallogo{\includegraphics{CP_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.357,0.039,0.475}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.clim-past-discuss.net}
    \def\@journallogo{\includegraphics{CPD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Climate of the Past (CP).\\ Please refer to the corresponding final paper in CP if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.357,0.039,0.475}
      \definecolor{buttonbackground}{rgb}{0.357,0.039,0.475}
      \definecolor{paneltext}{rgb}{0.357,0.039,0.475}
      \definecolor{buttontext}{rgb}{1.0,0.796,0.02}
    \fi
  \fi
\fi
\ifesd%classical
  \def\@journalname{Earth System Dynamics}
  \def\@journalnameabbreviation{Earth Syst. Dynam.}
  \def\@journalnameshort{ESD}
  \def\@journalnameshortlower{esd}
  \def\@journalstartyear{2010}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.earth-syst-dynam.net}
    \def\@journallogo{\includegraphics{ESD_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.0,0.224,0.455}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.0,0.224,0.455}
  \else
    \def\@journalurl{www.earth-syst-dynam-discuss.net}
    \def\@journallogo{\includegraphics{ESDD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Earth System\\ Dynamics (ESD). Please refer to the corresponding final paper in ESD if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.0,0.224,0.455}
      \definecolor{buttonbackground}{rgb}{0.0,0.224,0.455}
      \definecolor{paneltext}{rgb}{0.0,0.224,0.455}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifgi%classical
  \def\@journalname{Geoscientific Instrumentation, Methods and Data Systems}
  \def\@journalnameabbreviation{Geosci. Instrum. Method. Data Syst.}
  \def\@journalnameshort{GI}
  \def\@journalnameshortlower{gi}
  \def\@journalstartyear{2012}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.geosci-instrum-method-data-syst.net}
    \def\@journallogo{\includegraphics{GI_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.geosci-instrum-method-data-syst-discuss.net}
    \def\@journallogo{\includegraphics{GID_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Geoscientific Instrumentation,\\ Methods and Data Systems (GI). Please refer to the corresponding final paper in GI if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.012,0.306,0.635}
      \definecolor{buttonbackground}{rgb}{0.635,0.635,0.635}
      \definecolor{paneltext}{rgb}{0.012,0.306,0.635}
      \definecolor{buttontext}{rgb}{0.012,0.306,0.635}
    \fi
  \fi
\fi
\ifgmd%classical
  \def\@journalname{Geoscientific Model Development}
  \def\@journalnameabbreviation{Geosci. Model Dev.}
  \def\@journalnameshort{GMD}
  \def\@journalnameshortlower{gmd}
  \def\@journalstartyear{2008}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.geosci-model-dev.net}
    \def\@journallogo{\includegraphics{GMD_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.0,0.0,0.0}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.973,0.584,0.055}
  \else
    \def\@journalurl{www.geosci-model-dev-discuss.net}
    \def\@journallogo{\includegraphics{GMDD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Geoscientific Model\\ Development (GMD). Please refer to the corresponding final paper in GMD if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.973,0.584,0.055}
      \definecolor{buttonbackground}{rgb}{0.973,0.584,0.055}
      \definecolor{paneltext}{rgb}{0.973,0.584,0.055}
      \definecolor{buttontext}{rgb}{0.769,0.086,0.11}
    \fi
  \fi
\fi
\ifhess%classical
  \def\@journalname{Hydrology and Earth System Sciences}
  \def\@journalnameabbreviation{Hydrol. Earth Syst. Sci.}
  \def\@journalnameshort{HESS}
  \def\@journalnameshortlower{hess}
  \def\@journalstartyear{1997}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.hydrol-earth-syst-sci.net}
    \def\@journallogo{\includegraphics{HESS_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.hydrol-earth-syst-sci-discuss.net}
    \def\@journallogo{\includegraphics{HESSD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Hydrology and Earth System\\ Sciences (HESS). Please refer to the corresponding final paper in HESS if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.38,0.643,0.161}
      \definecolor{buttonbackground}{rgb}{0.38,0.643,0.161}
      \definecolor{paneltext}{rgb}{0.38,0.643,0.161}
      \definecolor{buttontext}{rgb}{0.122,0.153,0.498}
    \fi
  \fi
\fi
\ifnhess%classical
  \def\@journalname{Natural Hazards and Earth System Sciences}
  \def\@journalnameabbreviation{Nat. Hazards Earth Syst. Sci.}
  \def\@journalnameshort{NHESS}
  \def\@journalnameshortlower{nhess}
  \def\@journalstartyear{2001}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.nat-hazards-earth-syst-sci.net}
    \def\@journallogo{\includegraphics{NHESS_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.nat-hazards-earth-syst-sci-discuss.net}
    \def\@journallogo{\includegraphics{NHESSD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Natural Hazards and Earth\\ System Sciences (NHESS). Please refer to the corresponding final paper in NHESS if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.263,0.416,0.702}
      \definecolor{buttonbackground}{rgb}{0.69,0.639,0.576}
      \definecolor{paneltext}{rgb}{0.263,0.416,0.702}
      \definecolor{buttontext}{rgb}{0.263,0.416,0.702}
    \fi
  \fi
\fi
\ifnpg%classical
  \def\@journalname{Nonlinear Processes in Geophysics}
  \def\@journalnameabbreviation{Nonlin. Processes Geophys.}
  \def\@journalnameshort{NPG}
  \def\@journalnameshortlower{npg}
  \def\@journalstartyear{1994}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union \& the American Geophysical Union.}
  \if@stage@final
    \def\@journalurl{www.nonlin-processes-geophys.net}
    \def\@journallogo{\includegraphics{NPG_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.102,0.278,0.6}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.102,0.278,0.6}
  \else
    \def\@journalurl{www.nonlin-processes-geophys-discuss.net}
    \def\@journallogo{\includegraphics{NPGD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Nonlinear Processes\\ in Geophysics (NPG). Please refer to the corresponding final paper in NPG if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.102,0.278,0.6}
      \definecolor{buttonbackground}{rgb}{0.996,0.749,0.004}
      \definecolor{paneltext}{rgb}{0.102,0.278,0.6}
      \definecolor{buttontext}{rgb}{0.102,0.278,0.6}
    \fi
  \fi
\fi
\ifos%classical
  \def\@journalname{Ocean Science}
  \def\@journalnameabbreviation{Ocean Sci.}
  \def\@journalnameshort{OS}
  \def\@journalnameshortlower{os}
  \def\@journalstartyear{2005}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.ocean-sci.net}
    \def\@journallogo{\includegraphics{OS_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.ocean-sci-discuss.net}
    \def\@journallogo{\includegraphics{OSD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Ocean Science (OS).\\ Please refer to the corresponding final paper in OS if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.039,0.282,0.494}
      \definecolor{buttonbackground}{rgb}{0.039,0.282,0.494}
      \definecolor{paneltext}{rgb}{0.039,0.282,0.494}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifse%classical
  \def\@journalname{Solid Earth}
  \def\@journalnameabbreviation{Solid Earth}
  \def\@journalnameshort{SE}
  \def\@journalnameshortlower{se}
  \def\@journalstartyear{2010}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.solid-earth.net}
    \def\@journallogo{\includegraphics{SE_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.solid-earth-discuss.net}
    \def\@journallogo{\includegraphics{SED_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Solid Earth (SE).\\ Please refer to the corresponding final paper in SE if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.482,0.349,0.259}
      \definecolor{buttonbackground}{rgb}{0.482,0.349,0.259}
      \definecolor{paneltext}{rgb}{0.482,0.349,0.259}
      \definecolor{buttontext}{rgb}{0.98,0.922,0.835}
    \fi
  \fi
\fi
\iftc%classical
  \def\@journalname{The Cryosphere}
  \def\@journalnameabbreviation{The Cryosphere}
  \def\@journalnameshort{TC}
  \def\@journalnameshortlower{tc}
  \def\@journalstartyear{2007}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.the-cryosphere.net}
    \def\@journallogo{\includegraphics{TC_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.the-cryosphere-discuss.net}
    \def\@journallogo{\includegraphics{TCD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal The Cryosphere (TC).\\ Please refer to the corresponding final paper in TC if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.341,0.596,0.824}
      \definecolor{buttonbackground}{rgb}{0.341,0.596,0.824}
      \definecolor{paneltext}{rgb}{0.341,0.596,0.824}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifessd%classical
  \def\@journalname{Earth System Science Data}
  \def\@journalnameabbreviation{Earth Syst. Sci. Data}
  \def\@journalnameshort{ESSD}
  \def\@journalnameshortlower{essd}
  \def\@journalstartyear{2009}
  \def\@sentence{Published by Copernicus Publications.}
  \if@stage@final
    \def\@journalurl{www.earth-syst-sci-data.net}
    \def\@journallogo{\includegraphics{ESSD_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.102,0.4,0.573}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.earth-syst-sci-data-discuss.net}
    \def\@journallogo{\includegraphics{ESSDD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Earth System Science\\ Data (ESSD). Please refer to the corresponding final paper in ESSD if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.102,0.4,0.573}
      \definecolor{buttonbackground}{rgb}{0.102,0.4,0.573}
      \definecolor{paneltext}{rgb}{0.102,0.4,0.573}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifesurf%classical
  \def\@journalname{Earth Surface Dynamics}
  \def\@journalnameabbreviation{Earth Surf. Dynam.}
  \def\@journalnameshort{ESURF}
  \def\@journalnameshortlower{esurf}
  \def\@journalstartyear{2013}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.earth-surf-dynam.net}
    \def\@journallogo{\includegraphics{ESURF_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.541,0.549,0.557}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.earth-surf-dynam-discuss.net}
    \def\@journallogo{\includegraphics{ESURFD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Earth Surface Dynamics (ESurfD).\\ Please refer to the corresponding final paper in ESurf if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.0,0.0,0.0}
      \definecolor{buttonbackground}{rgb}{0.541,0.549,0.557}
      \definecolor{paneltext}{rgb}{0.0,0.0,0.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifgh%classical
  \def\@journalname{Geographica Helvetica}
  \def\@journalnameabbreviation{Geogr. Helv.}
  \def\@journalnameshort{GH}
  \def\@journalnameshortlower{gh}
  \def\@journalstartyear{1946}
  \def\@sentence{\small Published by Copernicus Publications for the Geographisch-Ethnographische Gesellschaft Z\"urich \& Association Suisse de G\'eographie.}
  \def\@journalurl{www.geogr-helv.net}
  \def\@journallogo{\includegraphics{GH_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.569,0.58,0.588}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.569,0.58,0.588}
\fi
\ifhgss%classical
  \def\@journalname{History of Geo- and Space Sciences}
  \def\@journalnameabbreviation{Hist. Geo Space Sci.}
  \def\@journalnameshort{HGSS}
  \def\@journalnameshortlower{hgss}
  \def\@journalstartyear{2010}
  \def\@sentence{Published by Copernicus Publications.}
  \if@stage@final
    \def\@journalurl{www.hist-geo-space-sci.net}
    \def\@journallogo{\includegraphics{HGSS_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.0,0.443,0.706}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.0,0.443,0.706}
  \else
    \def\@journalurl{https://hgss.copernicus.org/preprints/preprints.html}
    \def\@journallogo{\includegraphics{HGSSD_Logo.pdf}}
    \def\@sentenceDiscussion{This preprint is/has been under review for the journal History of Geo- and Space Sciences (HGSS). Please refer to the corresponding final paper in HGSS if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifjsss%classical
  \def\@journalname{Journal of Sensors and Sensor Systems}
  \def\@journalnameabbreviation{J. Sens. Sens. Syst.}
  \def\@journalnameshort{JSSS}
  \def\@journalnameshortlower{jsss}
  \def\@journalstartyear{2012}
  \def\@sentence{Published by Copernicus Publications on behalf of the AMA Association for Sensor Technology.}
  \def\@journalurl{www.j-sens-sens-syst.net}
  \def\@journallogo{\includegraphics{JSSS_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.051,0.298,0.561}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.051,0.298,0.561}
\fi
\ifms%classical
  \def\@journalname{Mechanical Sciences}
  \def\@journalnameabbreviation{Mech. Sci.}
  \def\@journalnameshort{MS}
  \def\@journalnameshortlower{ms}
  \def\@journalstartyear{2010}
  \def\@sentence{Published by Copernicus Publications.}
  \def\@journalurl{www.mech-sci.net}
  \def\@journallogo{\includegraphics{MS_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.471,0.655,0.808}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{0.0,0.0,0.0}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifasr%classical
  \def\@journalname{Advances in Science and Research}
  \def\@journalnameabbreviation{Adv. Sci. Res.}
  \def\@journalnameshort{ASR}
  \def\@journalnameshortlower{asr}
  \def\@journalstartyear{2007}
  \def\@sentence{Published by Copernicus Publications.}
  \def\@journalurl{www.adv-sci-res.net}
  \def\@journallogo{\includegraphics{ASR_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.004,0.302,0.545}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.004,0.302,0.545}
\fi
\ifsdsr%classical
  \def\@journalname{Scientific Drilling - Science Reports}
  \def\@journalnameabbreviation{Sci. Dril.}
  \def\@journalnameshort{SD}
  \def\@journalnameshortlower{sd}
  \def\@journalstartyear{2013}
  \def\@sentence{Published by Copernicus Publications on behalf of the IODP and the ICDP.}
  \def\@journalurl{www.sci-dril.net}
  \def\@journallogo{\includegraphics{SD_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.075,0.239,0.553}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.075,0.239,0.553}
\fi
\ifsdpr%classical
  \def\@journalname{Scientific Drilling - Progress Reports}
  \def\@journalnameabbreviation{Sci. Dril.}
  \def\@journalnameshort{SD}
  \def\@journalnameshortlower{sd}
  \def\@journalstartyear{2013}
  \def\@sentence{Published by Copernicus Publications on behalf of the IODP and the ICDP.}
  \def\@journalurl{www.sci-dril.net}
  \def\@journallogo{\includegraphics{SD_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.906,0.651,0.278}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.906,0.651,0.278}
\fi
\ifsdtd%classical
  \def\@journalname{Scientific Drilling - Technical Developments}
  \def\@journalnameabbreviation{Sci. Dril.}
  \def\@journalnameshort{SD}
  \def\@journalnameshortlower{sd}
  \def\@journalstartyear{2013}
  \def\@sentence{Published by Copernicus Publications on behalf of the IODP and the ICDP.}
  \def\@journalurl{www.sci-dril.net}
  \def\@journallogo{\includegraphics{SD_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.722,0.094,0.141}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{0.722,0.094,0.141}
\fi
\ifsdwr%classical
  \def\@journalname{Scientific Drilling - Workshop Reports}
  \def\@journalnameabbreviation{Sci. Dril.}
  \def\@journalnameshort{SD}
  \def\@journalnameshortlower{sd}
  \def\@journalstartyear{2013}
  \def\@sentence{Published by Copernicus Publications on behalf of the IODP and the ICDP.}
  \def\@journalurl{www.sci-dril.net}
  \def\@journallogo{\includegraphics{SD_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.486,0.298,0.216}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.486,0.298,0.216}
\fi
\ifsdpd%classical
  \def\@journalname{Scientific Drilling - Program Developments}
  \def\@journalnameabbreviation{Sci. Dril.}
  \def\@journalnameshort{SD}
  \def\@journalnameshortlower{sd}
  \def\@journalstartyear{2013}
  \def\@sentence{Published by Copernicus Publications on behalf of the IODP and the ICDP.}
  \def\@journalurl{www.sci-dril.net}
  \def\@journallogo{\includegraphics{SD_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.027,0.498,0.325}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.027,0.498,0.325}
\fi
\ifwe%classical
  \def\@journalname{Web Ecology}
  \def\@journalnameabbreviation{Web Ecol.}
  \def\@journalnameshort{WE}
  \def\@journalnameshortlower{we}
  \def\@journalstartyear{2001}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Ecological Federation (EEF).}
  \def\@journalurl{www.web-ecol.net}
  \def\@journallogo{\includegraphics{WE_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.2,0.6,0.2}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.2,0.6,0.2}
\fi
\ifangeocom%classical
  \def\@journalname{AnGeo Communicates}
  \def\@journalnameabbreviation{Ann. Geophys.}
  \def\@journalnameshort{ANGEOCOM}
  \def\@journalnameshortlower{angeo}
  \def\@journalstartyear{1983}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.ann-geophys.net}
    \def\@journallogo{\includegraphics{ANGEO_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{0.851,0.851,0.851}
    \definecolor{rulecol}{rgb}{0.0,0.0,0.0}
  \else
    \def\@journalurl{}
    \def\@journallogo{}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifsoil%classical
  \def\@journalname{SOIL}
  \def\@journalnameabbreviation{SOIL}
  \def\@journalnameshort{SOIL}
  \def\@journalnameshortlower{soil}
  \def\@journalstartyear{2015}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.soil-journal.net}
    \def\@journallogo{\includegraphics{SOIL_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.29,0.267,0.165}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.29,0.267,0.165}
  \else
    \def\@journalurl{www.soil-discuss.net}
    \def\@journallogo{\includegraphics{SOILD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal SOIL. Please refer to the\\ corresponding final paper in SOIL if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.29,0.267,0.165}
      \definecolor{buttonbackground}{rgb}{0.29,0.267,0.165}
      \definecolor{paneltext}{rgb}{0.29,0.267,0.165}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifpb%classical
  \def\@journalname{Primate Biology}
  \def\@journalnameabbreviation{Primate Biol.}
  \def\@journalnameshort{PB}
  \def\@journalnameshortlower{pb}
  \def\@journalstartyear{2014}
  \def\@sentence{Published by Copernicus Publications on behalf of the Deutsches Primatenzentrum GmbH (DPZ).}
  \def\@journalurl{www.primate-biol.net}
  \def\@journallogo{\includegraphics{PB_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.0,0.0,0.0}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{0.0,0.0,0.0}
\fi
\ifascmo%classical
  \def\@journalname{Advances in Statistical Climatology, Meteorology and Oceanography}
  \def\@journalnameabbreviation{Adv. Stat. Clim. Meteorol. Oceanogr.}
  \def\@journalnameshort{ASCMO}
  \def\@journalnameshortlower{ascmo}
  \def\@journalstartyear{2015}
  \def\@sentence{Published by Copernicus Publications.}
  \def\@journalurl{www.adv-stat-clim-meteorol-oceanogr.net}
  \def\@journallogo{\includegraphics{ASCMO_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.392,0.039,0.039}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.392,0.039,0.039}
\fi
\ifpiahs%classical
  \def\@journalname{Proceedings of the International Association of Hydrological Sciences}
  \def\@journalnameabbreviation{Proc. IAHS}
  \def\@journalnameshort{PIAHS}
  \def\@journalnameshortlower{piahs}
  \def\@journalstartyear{1924}
  \def\@sentence{Published by Copernicus Publications on behalf of the International Association of Hydrological Sciences.}
  \def\@journalurl{proc-iahs.net}
  \def\@journallogo{\includegraphics{PIAHS_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.553,0.035,0.035}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.553,0.035,0.035}
\fi
\ifaab%classical
  \def\@journalname{Archives Animal Breeding}
  \def\@journalnameabbreviation{Arch. Anim. Breed.}
  \def\@journalnameshort{AAB}
  \def\@journalnameshortlower{aab}
  \def\@journalstartyear{2015}
  \def\@sentence{Published by Copernicus Publications on behalf of the Research Institute for Farm Animal Biology (FBN).}
  \def\@journalurl{www.arch-anim-breed.net}
  \def\@journallogo{\includegraphics{AAB_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.6,0.0,0.0}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifwes%classical
  \def\@journalname{Wind Energy Science}
  \def\@journalnameabbreviation{Wind Energ. Sci.}
  \def\@journalnameshort{WES}
  \def\@journalnameshortlower{wes}
  \def\@journalstartyear{2016}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Academy of Wind Energy e.V.}
  \if@stage@final
    \def\@journalurl{www.wind-energ-sci.net}
    \def\@journallogo{\includegraphics{WES_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.075,0.263,0.565}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.075,0.263,0.565}
  \else
    \def\@journalurl{www.wind-energ-sci-discuss.net}
    \def\@journallogo{\includegraphics{WESD_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Wind Energy Science (WES).\\ Please refer to the corresponding final paper in WES if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.075,0.263,0.565}
      \definecolor{buttonbackground}{rgb}{0.075,0.263,0.565}
      \definecolor{paneltext}{rgb}{0.075,0.263,0.565}
      \definecolor{buttontext}{rgb}{0.584,0.761,0.239}
    \fi
  \fi
\fi
\ifjm%classical
  \def\@journalname{Journal of Micropalaeontology}
  \def\@journalnameabbreviation{J. Micropalaeontology}
  \def\@journalnameshort{JM}
  \def\@journalnameshortlower{jm}
  \def\@journalstartyear{1982}
  \def\@sentence{Published by Copernicus Publications on behalf of The Micropalaeontological Society.}
  \def\@journalurl{www.j-micropalaeontol.net/}
  \def\@journallogo{\includegraphics{JM_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.047,0.306,0.427}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{0.047,0.306,0.427}
\fi
\ifegqsj%classical
  \def\@journalname{E&G Quaternary Science Journal}
  \def\@journalnameabbreviation{E\&G Quaternary Sci. J.}
  \def\@journalnameshort{EGQSJ}
  \def\@journalnameshortlower{egqsj}
  \def\@journalstartyear{1951}
  \def\@sentence{Published by Copernicus Publications on behalf of the Deutsche Quart\"arvereinigung (DEUQUA) e.V.}
  \def\@journalurl{www.eg-quaternary-sci-j.net}
  \def\@journallogo{\includegraphics{EGQSJ_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.0,0.459,0.722}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifgc%classical
  \def\@journalname{Geoscience Communication}
  \def\@journalnameabbreviation{Geosci. Commun.}
  \def\@journalnameshort{GC}
  \def\@journalnameshortlower{gc}
  \def\@journalstartyear{2018}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.geosci-commun.net}
    \def\@journallogo{\includegraphics{GC_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.067,0.494,1.0}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{0.067,0.494,1.0}
  \else
    \def\@journalurl{www.geosci-commun-discuss.net}
    \def\@journallogo{\includegraphics{GCD_Logo.pdf}}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifdeuquasp%classical
  \def\@journalname{DEUQUA Special Publications}
  \def\@journalnameabbreviation{DEUQUA Spec. Pub.}
  \def\@journalnameshort{DEUQUASP}
  \def\@journalnameshortlower{deuquasp}
  \def\@journalstartyear{2018}
  \def\@sentence{Published by Copernicus Publications on behalf of the Deutsche Quart\"arvereinigung (DEUQUA) e.V.}
  \def\@journalurl{www.deuqua-spec-pub.net}
  \def\@journallogo{\includegraphics{DEUQUASP_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.529,0.533,0.541}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifgchron%classical
  \def\@journalname{Geochronology}
  \def\@journalnameabbreviation{Geochronology}
  \def\@journalnameshort{GCHRON}
  \def\@journalnameshortlower{gchron}
  \def\@journalstartyear{2019}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.geochronology.net}
    \def\@journallogo{\includegraphics{GCHRON_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.447,0.247,0.329}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.geochronology-discuss.net}
    \def\@journallogo{\includegraphics{GCHROND_Logo.pdf}}
    \def\@sentenceDiscussion{This discussion paper is/has been under review for the journal Geochronology (GChron).\\ Please refer to the corresponding final paper in GChron if available.}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifwcd%classical
  \def\@journalname{Weather and Climate Dynamics}
  \def\@journalnameabbreviation{Weather Clim. Dynam.}
  \def\@journalnameshort{WCD}
  \def\@journalnameshortlower{wcd}
  \def\@journalstartyear{2019}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{www.weather-clim-dynam.net}
    \def\@journallogo{\includegraphics{WCD_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{www.weather-clim-dynam-discuss.net}
    \def\@journallogo{\includegraphics{WCDD_Logo.pdf}}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifejm%classical
  \def\@journalname{European Journal of Mineralogy}
  \def\@journalnameabbreviation{Eur. J. Mineral.}
  \def\@journalnameshort{EJM}
  \def\@journalnameshortlower{ejm}
  \def\@journalstartyear{1989}
  \def\@sentence{Published by Copernicus Publications on behalf of the European mineralogical societies DMG, SEM, SIMP \& SFMC.}
  \def\@journalurl{www.eur-j-mineral.net}
  \def\@journallogo{\includegraphics{EJM_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.0,0.0,0.0}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifmr%classical
  \def\@journalname{Magnetic Resonance}
  \def\@journalnameabbreviation{Magn. Reson.}
  \def\@journalnameshort{MR}
  \def\@journalnameshortlower{mr}
  \def\@journalstartyear{2020}
  \def\@sentence{Published by Copernicus Publications on behalf of the Groupement AMPERE.}
  \if@stage@final
    \def\@journalurl{www.magn-reson.net}
    \def\@journallogo{\includegraphics{MR_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.627,0.098,0.086}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{}
    \def\@journallogo{\includegraphics{MRD_Logo.pdf}}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifjbji%classical
  \def\@journalname{Journal of Bone and Joint Infection}
  \def\@journalnameabbreviation{J. Bone Joint Infect.}
  \def\@journalnameshort{JBJI}
  \def\@journalnameshortlower{jbji}
  \def\@journalstartyear{2016}
  \def\@sentence{Published by Copernicus Publications on behalf of EBJIS and MSIS.}
  \def\@journalurl{https://jbji.copernicus.org/}
  \def\@journallogo{\includegraphics{JBJI_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.161,0.337,0.361}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{barcol}{rgb}{1.0,1.0,1.0}
  \definecolor{rulecol}{rgb}{0.161,0.337,0.361}
\fi
\ifpolf%classical
  \def\@journalname{Polarforschung - Journal of the German Society for Polar Research}
  \def\@journalnameabbreviation{Polarforschung}
  \def\@journalnameshort{POLF}
  \def\@journalnameshortlower{polf}
  \def\@journalstartyear{1931}
  \def\@sentence{Published by Copernicus Publications on behalf of the Deutsche Gesellschaft für Polarforschung e.V.}
  \def\@journalurl{https://polf.copernicus.org}
  \def\@journallogo{\includegraphics{POLF_Logo.pdf}}
  \definecolor{textcol}{rgb}{0.306,0.525,0.682}
  \definecolor{bgcol}{rgb}{1,1,1}
  \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
\fi
\ifsand%classical
  \def\@journalname{Safety of Nuclear Waste Disposal}
  \def\@journalnameabbreviation{Saf. Nucl. Waste Disposal}
  \def\@journalnameshort{SAND}
  \def\@journalnameshortlower{sand}
  \def\@journalstartyear{2021}
  \def\@sentence{Published by Copernicus Publications on behalf of the Federal Office for the Safety of Nuclear Waste Management (BASE).}
  \if@stage@final
    \def\@journalurl{www.sand.copernicus.org}
    \def\@journallogo{\includegraphics{SAND_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.0,0.0,0.0}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.0,0.0,0.0}
  \else
    \def\@journalurl{}
    \def\@journallogo{}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifegusphere%classical
  \def\@journalname{EGUsphere}
  \def\@journalnameabbreviation{EGUsphere}
  \def\@journalnameshort{EGUSPHERE}
  \def\@journalnameshortlower{egusphere}
  \def\@journalstartyear{2022}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Geosciences Union.}
  \if@stage@final
    \def\@journalurl{https://egusphere.copernicus.org}
    \def\@journallogo{\includegraphics{EGUSPHERE_Logo.pdf}}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{rulecol}{rgb}{1.0,1.0,1.0}
  \else
    \def\@journalurl{https://egusphere.copernicus.org}
    \def\@journallogo{\includegraphics{EGUSPHERE_Logo.pdf}}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{0.0,0.0,0.0}
      \definecolor{buttonbackground}{rgb}{0.0,0.0,0.0}
      \definecolor{paneltext}{rgb}{0.0,0.0,0.0}
      \definecolor{buttontext}{rgb}{0.0,0.0,0.0}
    \fi
  \fi
\fi
\ifsp%classical
  \def\@journalname{State of the Planet}
  \def\@journalnameabbreviation{State Planet}
  \def\@journalnameshort{SP}
  \def\@journalnameshortlower{sp}
  \def\@journalstartyear{2023}
  \def\@sentence{Published by Copernicus Publications.}
  \if@stage@final
    \def\@journalurl{https://sp.copernicus.org/}
    \def\@journallogo{\includegraphics{SP_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.008,0.239,0.365}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.008,0.239,0.365}
  \else
    \def\@journalurl{}
    \def\@journallogo{\includegraphics{SPD_Logo.pdf}}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi
\ifar%classical
  \def\@journalname{Aerosol Research}
  \def\@journalnameabbreviation{Aerosol Res.}
  \def\@journalnameshort{AR}
  \def\@journalnameshortlower{ar}
  \def\@journalstartyear{2023}
  \def\@sentence{Published by Copernicus Publications on behalf of the European Aerosol Assembly (EAA).}
  \if@stage@final
    \def\@journalurl{https://ar.copernicus.org/}
    \def\@journallogo{\includegraphics{AR_Logo.pdf}}
    \definecolor{textcol}{rgb}{0.369,0.522,0.671}
    \definecolor{bgcol}{rgb}{1,1,1}
    \definecolor{barcol}{rgb}{1.0,1.0,1.0}
    \definecolor{rulecol}{rgb}{0.369,0.522,0.671}
  \else
    \def\@journalurl{}
    \def\@journallogo{\includegraphics{ARD_Logo.pdf}}
    \def\@sentenceDiscussion{}
    \if@cop@home
      \definecolor{journalname}{rgb}{1.0,1.0,1.0}
      \definecolor{buttonbackground}{rgb}{1.0,1.0,1.0}
      \definecolor{paneltext}{rgb}{1.0,1.0,1.0}
      \definecolor{buttontext}{rgb}{1.0,1.0,1.0}
    \fi
  \fi
\fi

}